"use client"

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Transcription, CreateTranscriptionRequest } from '@/types/database';

interface UseTranscriptionResult {
  transcription: Transcription | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createOrUpdateTranscription: (transcriptionData: CreateTranscriptionRequest) => Promise<Transcription>;
}

export function useTranscription(interviewId: string): UseTranscriptionResult {
  const [transcription, setTranscription] = useState<Transcription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchTranscription = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/transcription`, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 404) {
          setTranscription(null);
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch transcription');
      }

      const data = await response.json();
      setTranscription(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setTranscription(null);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateTranscription = async (transcriptionData: CreateTranscriptionRequest): Promise<Transcription> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/transcription`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(await getAuthHeaders()),
        },
        body: JSON.stringify(transcriptionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create/update transcription');
      }

      const data = await response.json();
      const updatedTranscription = data.data;
      setTranscription(updatedTranscription);
      
      return updatedTranscription;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchTranscription();
    }
  }, [interviewId]);

  return {
    transcription,
    loading,
    error,
    refetch: fetchTranscription,
    createOrUpdateTranscription,
  };
}

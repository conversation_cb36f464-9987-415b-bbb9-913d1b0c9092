"use client"

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Interview, InterviewFilters, CreateInterviewRequest, UpdateInterviewRequest } from '@/types/database';

interface UseInterviewsResult {
  interviews: Interview[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createInterview: (caseId: string, interviewData: CreateInterviewRequest) => Promise<Interview>;
}

export function useInterviews(filters?: InterviewFilters): UseInterviewsResult {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchInterviews = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!filters?.caseId) {
        setInterviews([]);
        return;
      }

      const searchParams = new URLSearchParams();
      if (filters?.status) searchParams.append('status', filters.status);
      if (filters?.limit) searchParams.append('limit', filters.limit.toString());
      if (filters?.offset) searchParams.append('offset', filters.offset.toString());

      const url = `/api/cases/${filters.caseId}/interviews${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch interviews');
      }

      const data = await response.json();
      setInterviews(data.data.interviews || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setInterviews([]);
    } finally {
      setLoading(false);
    }
  };

  const createInterview = async (caseId: string, interviewData: CreateInterviewRequest): Promise<Interview> => {
    try {
      setError(null);

      const response = await fetch(`/api/cases/${caseId}/interviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(await getAuthHeaders()),
        },
        body: JSON.stringify(interviewData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create interview');
      }

      const data = await response.json();
      const newInterview = data.data;
      
      // Add the new interview to the local state
      setInterviews(prev => [newInterview, ...prev]);
      
      return newInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchInterviews();
  }, [filters?.caseId, filters?.status, filters?.limit, filters?.offset]);

  return {
    interviews,
    loading,
    error,
    refetch: fetchInterviews,
    createInterview,
  };
}

interface UseInterviewResult {
  interview: Interview | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateInterview: (updates: UpdateInterviewRequest) => Promise<Interview>;
  startInterview: () => Promise<Interview>;
  endInterview: () => Promise<Interview>;
  deleteInterview: () => Promise<void>;
}

export function useInterview(interviewId: string): UseInterviewResult {
  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchInterview = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}`, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 404) {
          setInterview(null);
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch interview');
      }

      const data = await response.json();
      setInterview(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setInterview(null);
    } finally {
      setLoading(false);
    }
  };

  const updateInterview = async (updates: UpdateInterviewRequest): Promise<Interview> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...(await getAuthHeaders()),
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update interview');
      }

      const data = await response.json();
      const updatedInterview = data.data;
      setInterview(updatedInterview);
      
      return updatedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const startInterview = async (): Promise<Interview> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/start`, {
        method: 'POST',
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to start interview');
      }

      const data = await response.json();
      const startedInterview = data.data;
      setInterview(startedInterview);
      
      return startedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const endInterview = async (): Promise<Interview> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/end`, {
        method: 'POST',
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to end interview');
      }

      const data = await response.json();
      const endedInterview = data.data;
      setInterview(endedInterview);
      
      return endedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteInterview = async (): Promise<void> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}`, {
        method: 'DELETE',
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete interview');
      }

      setInterview(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchInterview();
    }
  }, [interviewId]);

  return {
    interview,
    loading,
    error,
    refetch: fetchInterview,
    updateInterview,
    startInterview,
    endInterview,
    deleteInterview,
  };
}

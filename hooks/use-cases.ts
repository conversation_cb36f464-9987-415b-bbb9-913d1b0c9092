"use client"

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Case, CaseFilters, CreateCaseRequest } from '@/types/database';

interface UseCasesResult {
  cases: Case[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createCase: (caseData: CreateCaseRequest) => Promise<Case>;
}

export function useCases(filters?: CaseFilters): UseCasesResult {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchCases = async () => {
    try {
      setLoading(true);
      setError(null);

      const searchParams = new URLSearchParams();
      if (filters?.status) searchParams.append('status', filters.status);
      if (filters?.officer) searchParams.append('officer', filters.officer);
      if (filters?.limit) searchParams.append('limit', filters.limit.toString());
      if (filters?.offset) searchParams.append('offset', filters.offset.toString());

      const url = `/api/cases${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      
      const response = await fetch(url, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch cases');
      }

      const data = await response.json();
      setCases(data.data.cases || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setCases([]);
    } finally {
      setLoading(false);
    }
  };

  const createCase = async (caseData: CreateCaseRequest): Promise<Case> => {
    try {
      setError(null);

      const response = await fetch('/api/cases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(await getAuthHeaders()),
        },
        body: JSON.stringify(caseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create case');
      }

      const data = await response.json();
      const newCase = data.data;
      
      // Add the new case to the local state
      setCases(prev => [newCase, ...prev]);
      
      return newCase;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchCases();
  }, [filters?.status, filters?.officer, filters?.limit, filters?.offset]);

  return {
    cases,
    loading,
    error,
    refetch: fetchCases,
    createCase,
  };
}

interface UseCaseResult {
  case: Case | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useCase(caseId: string): UseCaseResult {
  const [caseData, setCaseData] = useState<Case | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchCase = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/cases/${caseId}`, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 404) {
          setCaseData(null);
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch case');
      }

      const data = await response.json();
      setCaseData(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setCaseData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (caseId) {
      fetchCase();
    }
  }, [caseId]);

  return {
    case: caseData,
    loading,
    error,
    refetch: fetchCase,
  };
}

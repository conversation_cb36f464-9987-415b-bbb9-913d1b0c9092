"use client"

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Statement } from '@/types/database';

interface CreateStatementRequest {
  content: string;
  officer_notes?: string;
}

interface UseStatementResult {
  statement: Statement | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createOrUpdateStatement: (statementData: CreateStatementRequest) => Promise<Statement>;
}

export function useStatement(interviewId: string): UseStatementResult {
  const [statement, setStatement] = useState<Statement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getAuthHeaders } = useAuth();

  const fetchStatement = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/statement`, {
        headers: await getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 404) {
          setStatement(null);
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch statement');
      }

      const data = await response.json();
      setStatement(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setStatement(null);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateStatement = async (statementData: CreateStatementRequest): Promise<Statement> => {
    try {
      setError(null);

      const response = await fetch(`/api/interviews/${interviewId}/statement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(await getAuthHeaders()),
        },
        body: JSON.stringify(statementData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create/update statement');
      }

      const data = await response.json();
      const updatedStatement = data.data;
      setStatement(updatedStatement);
      
      return updatedStatement;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchStatement();
    }
  }, [interviewId]);

  return {
    statement,
    loading,
    error,
    refetch: fetchStatement,
    createOrUpdateStatement,
  };
}
